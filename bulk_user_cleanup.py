#!/usr/bin/env python3
"""
Bulk User Cleanup Script for Telegram Bot Database

This script efficiently removes dead users from the MongoDB database by:
1. Reading user IDs from 'dead users.txt' file in memory-efficient chunks
2. Processing deletions in optimized batches
3. Providing real-time progress tracking with ETA
4. Supporting resume functionality from specific positions
5. Implementing comprehensive error handling and logging

Usage:
    python bulk_user_cleanup.py [--start-position N] [--batch-size N] [--dry-run]
"""

import sys
import time
import logging
import argparse
from datetime import datetime, timedelta
from typing import List, Tuple, Optional
from pymongo import MongoClient
from pymongo.errors import BulkWriteError, ConnectionFailure
from config import MONGODB_URI, DB_NAME

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'bulk_cleanup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class BulkUserCleanup:
    """Efficient bulk user deletion with progress tracking and resume capability."""
    
    def __init__(self, dead_users_file: str = "dead users.txt",
                 batch_size: int = 5000, dry_run: bool = False, remove_from_file: bool = True):
        """
        Initialize the cleanup processor.

        Args:
            dead_users_file: Path to file containing dead user IDs
            batch_size: Number of users to process per batch (optimal: 2000-10000 for speed)
            dry_run: If True, only simulate deletions without actual database changes
            remove_from_file: If True, remove processed IDs from the file
        """
        self.dead_users_file = dead_users_file
        self.batch_size = batch_size
        self.dry_run = dry_run
        self.remove_from_file = remove_from_file
        self.client = None
        self.db = None
        self.users_collection = None
        
        # Statistics tracking
        self.stats = {
            'total_processed': 0,
            'successfully_deleted': 0,
            'not_found': 0,
            'errors': 0,
            'start_time': None,
            'batches_completed': 0
        }
        
        # Progress tracking
        self.total_users_to_process = 0
        self.current_position = 0
        
    def connect_to_database(self) -> bool:
        """Establish MongoDB connection with optimized settings."""
        try:
            self.client = MongoClient(
                MONGODB_URI,
                maxPoolSize=50,  # Increased pool size for maximum speed
                minPoolSize=10,
                maxIdleTimeMS=60000,
                serverSelectionTimeoutMS=5000,
                connectTimeoutMS=10000,
                socketTimeoutMS=60000,
                retryWrites=True,
                w=1  # Faster write acknowledgment
            )
            
            # Test connection
            self.client.admin.command('ping')
            self.db = self.client[DB_NAME]
            self.users_collection = self.db['users']
            
            logger.info("✅ Successfully connected to MongoDB")
            return True
            
        except ConnectionFailure as e:
            logger.error(f"❌ Failed to connect to MongoDB: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected database connection error: {e}")
            return False
    
    def get_total_user_count(self) -> int:
        """Get total number of users in dead users file."""
        try:
            with open(self.dead_users_file, 'r') as f:
                count = sum(1 for _ in f)
            logger.info(f"📊 Total dead users to process: {count:,}")
            return count
        except FileNotFoundError:
            logger.error(f"❌ File not found: {self.dead_users_file}")
            return 0
        except Exception as e:
            logger.error(f"❌ Error reading file: {e}")
            return 0
    
    def read_and_remove_batch(self) -> List[int]:
        """
        Read a batch of user IDs from the beginning of file and remove them.
        This is much faster than seeking to positions.

        Returns:
            List of user IDs as integers
        """
        user_ids = []
        remaining_lines = []

        try:
            # Read all lines
            with open(self.dead_users_file, 'r') as f:
                all_lines = f.readlines()

            if not all_lines:
                return []

            # Process batch from beginning
            batch_lines = all_lines[:self.batch_size]
            remaining_lines = all_lines[self.batch_size:]

            # Parse user IDs
            for line in batch_lines:
                try:
                    user_id = int(line.strip())
                    if user_id > 0:
                        user_ids.append(user_id)
                except ValueError:
                    logger.warning(f"⚠️ Invalid user ID format: {line.strip()}")

            # Write remaining lines back to file if remove_from_file is enabled
            if self.remove_from_file and not self.dry_run:
                with open(self.dead_users_file, 'w') as f:
                    f.writelines(remaining_lines)

        except Exception as e:
            logger.error(f"❌ Error processing file: {e}")

        return user_ids
    
    def delete_users_batch(self, user_ids: List[int]) -> Tuple[int, int, int]:
        """
        Delete a batch of users from database.
        
        Args:
            user_ids: List of user IDs to delete
            
        Returns:
            Tuple of (deleted_count, not_found_count, error_count)
        """
        if not user_ids:
            return 0, 0, 0
            
        if self.dry_run:
            # Simulate deletion for dry run
            logger.info(f"🔍 DRY RUN: Would delete {len(user_ids)} users")
            return len(user_ids), 0, 0
        
        try:
            # Use delete_many with unordered bulk operations for maximum speed
            result = self.users_collection.delete_many(
                {"user_id": {"$in": user_ids}},
                hint={"user_id": 1}  # Use index hint for faster queries
            )

            deleted_count = result.deleted_count
            not_found_count = len(user_ids) - deleted_count

            if deleted_count > 0:
                logger.debug(f"🗑️ Deleted {deleted_count} users from database")
            if not_found_count > 0:
                logger.debug(f"⚠️ {not_found_count} users not found in database")

            return deleted_count, not_found_count, 0
            
        except BulkWriteError as e:
            logger.error(f"❌ Bulk write error: {e}")
            return 0, 0, len(user_ids)
        except Exception as e:
            logger.error(f"❌ Database deletion error: {e}")
            return 0, 0, len(user_ids)
    
    def calculate_eta(self) -> str:
        """Calculate estimated time of completion."""
        if self.stats['batches_completed'] == 0:
            return "Calculating..."
            
        elapsed_time = time.time() - self.stats['start_time']
        avg_time_per_batch = elapsed_time / self.stats['batches_completed']
        
        remaining_batches = (self.total_users_to_process - self.current_position) // self.batch_size
        eta_seconds = remaining_batches * avg_time_per_batch
        
        eta_delta = timedelta(seconds=int(eta_seconds))
        return str(eta_delta)
    
    def print_progress(self, batch_num: int, total_batches: int, 
                      deleted: int, not_found: int, errors: int):
        """Print detailed progress information."""
        progress_percent = (self.current_position / self.total_users_to_process) * 100
        eta = self.calculate_eta()
        
        print(f"\n📊 Batch {batch_num:,}/{total_batches:,} "
              f"({progress_percent:.1f}% complete)")
        print(f"🗑️ Deleted: {deleted:,} | ⚠️ Not found: {not_found:,} | ❌ Errors: {errors:,}")
        print(f"📈 Total processed: {self.stats['total_processed']:,}")
        print(f"⏱️ ETA: {eta}")
        print("-" * 60)

    def run_cleanup(self, start_position: int = 0) -> bool:
        """
        Execute the bulk cleanup process.

        Args:
            start_position: Position in file to start processing from

        Returns:
            True if cleanup completed successfully
        """
        logger.info("🚀 Starting bulk user cleanup process")

        # Initialize
        if not self.connect_to_database():
            return False

        self.total_users_to_process = self.get_total_user_count()
        if self.total_users_to_process == 0:
            logger.error("❌ No users to process")
            return False

        self.current_position = start_position
        self.stats['start_time'] = time.time()

        # Calculate batches
        remaining_users = self.total_users_to_process - start_position
        total_batches = (remaining_users + self.batch_size - 1) // self.batch_size

        logger.info(f"📋 Processing {remaining_users:,} users in {total_batches:,} batches")
        logger.info(f"📦 Batch size: {self.batch_size:,}")
        logger.info(f"🎯 Starting from position: {start_position:,}")

        if self.dry_run:
            logger.info("🔍 DRY RUN MODE - No actual deletions will be performed")

        print("=" * 80)

        try:
            batch_num = 0
            while True:
                batch_num += 1

                # Read and remove batch of user IDs from file
                user_ids = self.read_and_remove_batch()
                if not user_ids:
                    logger.info("✅ Reached end of file")
                    break

                # Delete users
                deleted, not_found, errors = self.delete_users_batch(user_ids)

                # Update statistics
                self.stats['total_processed'] += len(user_ids)
                self.stats['successfully_deleted'] += deleted
                self.stats['not_found'] += not_found
                self.stats['errors'] += errors
                self.stats['batches_completed'] += 1

                # Update position
                self.current_position += len(user_ids)

                # Print progress every batch
                self.print_progress(batch_num, total_batches, deleted, not_found, errors)

                # No delay for maximum speed

        except KeyboardInterrupt:
            logger.warning("⚠️ Process interrupted by user")
            self.print_final_summary()
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error during cleanup: {e}")
            self.print_final_summary()
            return False
        finally:
            if self.client:
                self.client.close()
                logger.info("🔌 Database connection closed")

        self.print_final_summary()
        return True

    def print_final_summary(self):
        """Print comprehensive final summary."""
        elapsed_time = time.time() - self.stats['start_time'] if self.stats['start_time'] else 0

        print("\n" + "=" * 80)
        print("📋 BULK CLEANUP SUMMARY")
        print("=" * 80)
        print(f"⏱️ Total time: {timedelta(seconds=int(elapsed_time))}")
        print(f"📊 Total processed: {self.stats['total_processed']:,}")
        print(f"🗑️ Successfully deleted: {self.stats['successfully_deleted']:,}")
        print(f"⚠️ Not found in database: {self.stats['not_found']:,}")
        print(f"❌ Errors: {self.stats['errors']:,}")
        print(f"📦 Batches completed: {self.stats['batches_completed']:,}")

        if elapsed_time > 0:
            rate = self.stats['total_processed'] / elapsed_time
            print(f"⚡ Processing rate: {rate:.1f} users/second")

        if self.current_position < self.total_users_to_process:
            print(f"📍 Stopped at position: {self.current_position:,}")
            print(f"💡 To resume: python bulk_user_cleanup.py --start-position {self.current_position}")

        print("=" * 80)


def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(
        description="Bulk cleanup of dead users from Telegram bot database",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python bulk_user_cleanup.py                    # Process all users
  python bulk_user_cleanup.py --batch-size 2000  # Use larger batches
  python bulk_user_cleanup.py --start-position 100000  # Resume from position
  python bulk_user_cleanup.py --dry-run          # Test run without deletions
        """
    )

    parser.add_argument(
        '--start-position',
        type=int,
        default=0,
        help='Position in file to start processing from (default: 0)'
    )

    parser.add_argument(
        '--batch-size',
        type=int,
        default=5000,
        help='Number of users to process per batch (default: 5000, recommended: 2000-10000 for speed)'
    )

    parser.add_argument(
        '--no-remove-from-file',
        action='store_true',
        help='Do not remove processed user IDs from the file'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Simulate deletions without making actual database changes'
    )

    parser.add_argument(
        '--file',
        default='dead users.txt',
        help='Path to file containing dead user IDs (default: dead users.txt)'
    )

    args = parser.parse_args()

    # Validate arguments
    if args.start_position < 0:
        print("❌ Error: start-position must be >= 0")
        sys.exit(1)

    if args.batch_size < 1 or args.batch_size > 10000:
        print("❌ Error: batch-size must be between 1 and 10000")
        sys.exit(1)

    # Create and run cleanup
    cleanup = BulkUserCleanup(
        dead_users_file=args.file,
        batch_size=args.batch_size,
        dry_run=args.dry_run,
        remove_from_file=not args.no_remove_from_file
    )

    success = cleanup.run_cleanup(args.start_position)
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
