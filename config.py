import os
from dotenv import load_dotenv

load_dotenv()

BOT_TOKEN = os.getenv("BOT_TOKEN")
MONGODB_URI = os.getenv("MONGODB_URI")
ADMIN_CHAT_ID = int(os.getenv("ADMIN_CHAT_ID", 0))
MAIN_CHANNEL = os.getenv("MAIN_CHANNEL", "")
MAIN_CHANNEL_ID = int(os.getenv("MAIN_CHANNEL_ID"))
SECOND_CHANNEL = os.getenv("SECOND_CHANNEL")
SECOND_CHANNEL_ID = int(os.getenv("SECOND_CHANNEL_ID"))
SUPPORT_BOT = os.getenv("SUPPORT_BOT", "BotSupport")
DB_NAME = os.getenv("DB_NAME", "copyrightbot")
BOT_USERNAME = os.getenv("BOT_USERNAME")

# URLs for adding bot to channels/groups with permissions
ADD_CHANNEL_URL = os.getenv("ADD_CHANNEL_URL", "https://t.me/copyright_infrigment_saver_bot?startchannel=&admin=post_messages+edit_messages+promote_members+delete_messages+restrict_members+invite_users+pin_messages+manage_video_chats+change_info")
ADD_GROUP_URL = os.getenv("ADD_GROUP_URL", "https://t.me/copyright_infrigment_saver_bot?startgroup&admin=post_messages+edit_messages+promote_members+delete_messages+restrict_members+invite_users+pin_messages+manage_video_chats+change_info")

# Backup configuration
def _safe_int_env(env_var, default=0):
    """Safely convert environment variable to int, handling empty strings"""
    value = os.getenv(env_var, str(default))
    if not value or value.strip() == "":
        return default
    try:
        return int(value)
    except ValueError:
        return default

# Small files are forwarded to a user's private chat
BACKUP_SMALL_FILES_USER = _safe_int_env("BACKUP_SMALL_FILES_USER", 0)
# Large files continue to use channels
BACKUP_LARGE_FILES_CHANNEL = _safe_int_env("BACKUP_LARGE_FILES_CHANNEL", 0)

# Backward compatibility: support old BACKUP_SMALL_FILES_CHANNEL variable name
if BACKUP_SMALL_FILES_USER == 0:
    BACKUP_SMALL_FILES_USER = _safe_int_env("BACKUP_SMALL_FILES_CHANNEL", 0)

# Limits
FREE_CHANNEL_LIMIT = 6
