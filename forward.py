"""
Media Forwarding Module for Automatic Backups

This module handles automatic silent backup of media files from channels, groups, and private chats
to secure storage channels based on file size.
"""

import os
import json
import logging
import hashlib
import asyncio
import time
from datetime import datetime
from typing import Dict, <PERSON>, Optional, Union, Tuple, Any
import tempfile
import uuid

from telegram import (
    Update, Message, Bot, Chat, User,
    MessageEntity, Document, Video, Animation
)
from telegram.error import (
    TelegramError, BadRequest, TimedOut, 
    NetworkError, ChatMigrated, Forbidden, RetryAfter
)
from telegram.constants import ParseMode
from telegram.ext import ContextTypes, MessageHandler, filters

from config import BACKUP_SMALL_FILES_USER, BACKUP_LARGE_FILES_CHANNEL, ADMIN_CHAT_ID

# Global state for forwarding (conditional based on backup channel configuration)
# These will be set by validate_backup_channels()
FORWARDING_ENABLED = False
SMALL_FILES_FORWARDING_ENABLED = False
LARGE_FILES_FORWARDING_ENABLED = False

# Configure logging
logger = logging.getLogger("forward")
logger.setLevel(logging.INFO)

# Configure console handler only (no file logging)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.WARNING)
console_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(console_handler)

# File handlers are disabled as requested by user
# Uncomment below if file logging is needed again
# file_handler = logging.FileHandler("forward.log")
# file_handler.setLevel(logging.CRITICAL)  # Only log critical errors
# file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
# logger.addHandler(file_handler)
# 
# error_handler = logging.FileHandler("error.log")
# error_handler.setLevel(logging.CRITICAL)  # Only log critical errors
# error_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
# logger.addHandler(error_handler)

# Prevent propagation to root logger
logger.propagate = False

# Constants
TELEGRAM_BOT_MAX_FILE_SIZE = 50 * 1024 * 1024  # ~50MB practical limit for bot uploads
TELEGRAM_MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB absolute maximum for Telegram
DEDUP_FILE = "dedup.json"
MAX_RETRY_ATTEMPTS = 3
RETRY_DELAY = 2  # seconds

def validate_backup_channels():
    """
    Validate backup configuration and determine which forwarding features should be enabled.

    Returns:
        tuple: (small_files_enabled, large_files_enabled, overall_enabled)
    """
    global SMALL_FILES_FORWARDING_ENABLED, LARGE_FILES_FORWARDING_ENABLED, FORWARDING_ENABLED

    # Check small files user (forwarded to user's private chat)
    small_files_enabled = bool(BACKUP_SMALL_FILES_USER and BACKUP_SMALL_FILES_USER != 0)

    # Check large files channel
    large_files_enabled = bool(BACKUP_LARGE_FILES_CHANNEL and BACKUP_LARGE_FILES_CHANNEL != 0)

    # Overall forwarding is enabled if at least one destination is configured
    overall_enabled = small_files_enabled or large_files_enabled

    # Update global state
    SMALL_FILES_FORWARDING_ENABLED = small_files_enabled
    LARGE_FILES_FORWARDING_ENABLED = large_files_enabled
    FORWARDING_ENABLED = overall_enabled

    # Log configuration status
    if not overall_enabled:
        logger.warning("Media forwarding completely disabled - no backup destinations configured")
        logger.warning("Set BACKUP_SMALL_FILES_USER and/or BACKUP_LARGE_FILES_CHANNEL environment variables")
    else:
        logger.info("Media forwarding configuration:")
        if small_files_enabled:
            logger.info(f"  ✅ Small files forwarding enabled (User ID: {BACKUP_SMALL_FILES_USER})")
        else:
            logger.warning(f"  ❌ Small files forwarding disabled (BACKUP_SMALL_FILES_USER not configured)")

        if large_files_enabled:
            logger.info(f"  ✅ Large files forwarding enabled (Channel ID: {BACKUP_LARGE_FILES_CHANNEL})")
        else:
            logger.warning(f"  ❌ Large files forwarding disabled (BACKUP_LARGE_FILES_CHANNEL not configured)")

    return small_files_enabled, large_files_enabled, overall_enabled

def is_forwarding_enabled_for_file_size(file_size: int) -> bool:
    """
    Check if forwarding is enabled for a specific file size.

    Args:
        file_size (int): File size in bytes

    Returns:
        bool: True if forwarding is enabled for this file size
    """
    if file_size >= TELEGRAM_MAX_FILE_SIZE:  # 100MB
        return LARGE_FILES_FORWARDING_ENABLED
    else:
        return SMALL_FILES_FORWARDING_ENABLED

def get_forwarding_status() -> dict:
    """
    Get current forwarding status information.

    Returns:
        dict: Status information about forwarding configuration
    """
    return {
        "overall_enabled": FORWARDING_ENABLED,
        "small_files_enabled": SMALL_FILES_FORWARDING_ENABLED,
        "large_files_enabled": LARGE_FILES_FORWARDING_ENABLED,
        "small_files_user": BACKUP_SMALL_FILES_USER if SMALL_FILES_FORWARDING_ENABLED else None,
        "large_files_channel": BACKUP_LARGE_FILES_CHANNEL if LARGE_FILES_FORWARDING_ENABLED else None
    }

# Media types to ignore
DOCUMENT_MIME_TYPES_TO_IGNORE = [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
    "image/bmp",
    "image/tiff",
    "image/x-icon"
]

# Initialize forwarding configuration on module load
validate_backup_channels()

class EnhancedDedup:
    """Enhanced deduplication system for media files"""
    
    def __init__(self, dedup_file=DEDUP_FILE):
        self.dedup_file = dedup_file
        self.dedup_data = self._load_dedup_data()
        
    def _load_dedup_data(self) -> List[str]:
        """Load deduplication data from file"""
        if os.path.exists(self.dedup_file):
            try:
                with open(self.dedup_file, 'r') as f:
                    data = json.load(f)
                    # Handle conversion from old format to new format
                    return self._convert_dedup_data(data)
            except (json.JSONDecodeError, IOError) as e:
                logger.error(f"Error loading dedup file: {e}")
                return []
        else:
            return []
            
    def _convert_dedup_data(self, data) -> List[str]:
        """Convert dedup data from old format to new format if needed"""
        # If it's already a list, assume it's in the new format
        if isinstance(data, list):
            return data
            
        # Old format was a dict with a 'files' key containing file_unique_ids as keys
        if isinstance(data, dict) and 'files' in data:
            logger.info("Converting deduplication data from old format to new format")
            file_ids = list(data['files'].keys())
            logger.info(f"Converted {len(file_ids)} entries to new format")
            return file_ids
            
        # Unrecognized format, return empty list
        logger.warning(f"Unrecognized deduplication data format: {type(data)}")
        return []
            
    def _save_dedup_data(self):
        """Save deduplication data to file"""
        try:
            self._cleanup_if_needed()
            with open(self.dedup_file, 'w') as f:
                json.dump(self.dedup_data, f)
        except Exception as e:
            logger.error(f"Error saving dedup file: {e}")
            
    def _cleanup_if_needed(self, max_entries=1000):
        """Remove old entries if needed"""
        if len(self.dedup_data) > max_entries:
            # Remove oldest entries (beginning of list)
            to_remove = len(self.dedup_data) - max_entries
            self.dedup_data = self.dedup_data[to_remove:]
            logger.info(f"Cleaned up {to_remove} old entries from dedup file")
    
    def is_duplicate(self, file_unique_id: str) -> bool:
        """Check if a file has been processed before using unique_id"""
        return file_unique_id in self.dedup_data
        
    def add_to_dedup(self, file_unique_id: str, destination_info: Dict):
        """Add a file to the deduplication database"""
        if file_unique_id not in self.dedup_data:
            self.dedup_data.append(file_unique_id)
            self._save_dedup_data()

class SharedHostQueue:
    """Queue for processing media on shared hosts"""
    
    def __init__(self, queue_file="media_queue.json"):
        self.queue_file = queue_file
        self.queue_data = self._load_queue()
        self._lock = asyncio.Lock()
        
    def _load_queue(self) -> Dict:
        """Load queue from file"""
        if os.path.exists(self.queue_file):
            try:
                with open(self.queue_file, 'r') as f:
                    return json.load(f)
            except:
                return {"queue": [], "processing": [], "failed": [], "last_updated": int(time.time())}
        else:
            return {"queue": [], "processing": [], "failed": [], "last_updated": int(time.time())}
            
    def _save_queue(self):
        """Save queue to file"""
        try:
            with open(self.queue_file, 'w') as f:
                self.queue_data["last_updated"] = int(time.time())
                json.dump(self.queue_data, f)
        except Exception as e:
            logger.error(f"Error saving queue file: {e}")
    
    def _extract_message_data(self, message: Message) -> Dict:
        """Extract serializable data from Message object"""
        if not message:
            return {}
            
        # Extract basic message data
        data = {
            "message_id": message.message_id,
            "chat_id": message.chat_id,
            "date": message.date.isoformat() if message.date else None,
        }
        
        # Extract document info if available
        if message.document:
            data["document"] = {
                "file_id": message.document.file_id,
                "file_unique_id": message.document.file_unique_id,
                "file_size": message.document.file_size,
                "mime_type": message.document.mime_type,
                "file_name": getattr(message.document, "file_name", None)
            }
            
        # Extract video info if available
        if message.video:
            data["video"] = {
                "file_id": message.video.file_id,
                "file_unique_id": message.video.file_unique_id,
                "file_size": message.video.file_size,
                "width": message.video.width,
                "height": message.video.height,
                "duration": message.video.duration
            }
            
        return data
            
    async def add_to_queue(self, message: Message):
        """Add message to processing queue with data extraction"""
        async with self._lock:
            if "queue" not in self.queue_data:
                self.queue_data["queue"] = []
                
            # Extract only serializable data
            message_data = self._extract_message_data(message)
            self.queue_data["queue"].append(message_data)
            self._save_queue()
            
    async def get_next(self):
        """Get next item from queue"""
        async with self._lock:
            if not self.queue_data.get("queue"):
                return None
                
            item = self.queue_data["queue"].pop(0)
            if "processing" not in self.queue_data:
                self.queue_data["processing"] = []
            self.queue_data["processing"].append(item)
            self._save_queue()
            return item
            
    async def mark_completed(self, item):
        """Mark item as completed"""
        async with self._lock:
            if "processing" in self.queue_data:
                try:
                    self.queue_data["processing"].remove(item)
                except ValueError:
                    pass
            self._save_queue()
            
    async def mark_failed(self, item, error=None):
        """Mark item as failed"""
        async with self._lock:
            if "processing" in self.queue_data:
                try:
                    self.queue_data["processing"].remove(item)
                except ValueError:
                    pass
                    
            if "failed" not in self.queue_data:
                self.queue_data["failed"] = []
                
            if error:
                item["error"] = str(error)
                
            self.queue_data["failed"].append(item)
            self._save_queue()

class MediaProcessor:
    """Process and forward media files to backup channels"""
    
    def __init__(self, bot):
        self.bot = bot
        self.dedup = EnhancedDedup()
        self.queue = SharedHostQueue()
        
    def get_file_info(self, message: Message) -> Dict[str, Any]:
        """Extract file information from a message"""
        result = {
            "has_media": False,
            "message_id": message.message_id,
            "chat_id": message.chat_id,
            "date": message.date,
            "caption": message.caption or ""
        }
        
        # Check for channel message
        if message.forward_from_chat and message.forward_from_chat.type == "channel":
            channel_name = message.forward_from_chat.title
            channel_username = message.forward_from_chat.username
            if channel_username:
                # Channel message detected
                pass
        
        # Check for video
        if message.video:
            video = message.video
            result.update({
                "has_media": True,
                "media_type": "video",
                "file_id": video.file_id,
                "file_unique_id": video.file_unique_id,
                "file_size": video.file_size,
                "width": video.width,
                "height": video.height,
                "duration": video.duration,
                "mime_type": video.mime_type
            })
            # Video unique ID detected
            return result
            
        # Check for document
        elif message.document:
            document = message.document
            mime_type = document.mime_type or ""
            
            # Skip certain document types
            if mime_type in DOCUMENT_MIME_TYPES_TO_IGNORE:
                return result
                
            result.update({
                "has_media": True,
                "media_type": "document",
                "file_id": document.file_id,
                "file_unique_id": document.file_unique_id,
                "file_size": document.file_size,
                "mime_type": mime_type,
                "file_name": getattr(document, "file_name", None)
            })
            # Document unique ID detected
            return result
            
        # Check for animation/GIF
        elif message.animation:
            animation = message.animation
            result.update({
                "has_media": True,
                "media_type": "animation",
                "file_id": animation.file_id,
                "file_unique_id": animation.file_unique_id,
                "file_size": animation.file_size,
                "width": animation.width,
                "height": animation.height,
                "duration": animation.duration,
                "mime_type": animation.mime_type
            })
            # Animation unique ID detected
            return result
            
        # File info extraction completed
        return result
        
    def should_process(self, file_info: Dict) -> bool:
        """Determine if a file should be processed"""
        # Check if it has media
        if not file_info.get("has_media"):
            return False
            
        # Check if it's a PDF (skip PDFs)
        mime_type = file_info.get("mime_type", "")
        if mime_type in DOCUMENT_MIME_TYPES_TO_IGNORE:
            return False
            
        # Check if it's already been processed
        if self.dedup.is_duplicate(file_info.get("file_unique_id", "")):
            logger.info(f"Skipping duplicate file: {file_info.get('file_unique_id', '')}")
            return False
            
        return True
        
    def get_target_destination(self, file_info: Dict) -> Optional[int]:
        """Determine target destination (user or channel) based on file size and configuration"""
        file_size = file_info.get("file_size", 0)

        # Route based on size threshold and check if forwarding is enabled
        if file_size >= TELEGRAM_MAX_FILE_SIZE:  # 100MB
            if LARGE_FILES_FORWARDING_ENABLED:
                return BACKUP_LARGE_FILES_CHANNEL
            else:
                logger.debug(f"Large files forwarding disabled, skipping file ({file_size / (1024*1024):.2f} MB)")
                return None
        else:
            if SMALL_FILES_FORWARDING_ENABLED:
                return BACKUP_SMALL_FILES_USER
            else:
                logger.debug(f"Small files forwarding disabled, skipping file ({file_size / (1024*1024):.2f} MB)")
                return None
            
    async def build_extra_caption(self, file_info: Dict) -> str:
        """Build minimal caption with only source information"""
        original_caption = file_info.get("caption", "")
        
        # Source info
        source_info = ""
        
        # Add source info based on chat type
        chat_type = file_info.get("chat_type")
        if chat_type == "channel":
            # Use channel_info if available
            channel_info = file_info.get("channel_info", {})
            if channel_info:
                channel_username = channel_info.get("username", "")
                if channel_username:
                    source_info = f"\n\nhttps://t.me/{channel_username}"
                else:
                    source_info = f"\n\nPrivate Channel"
        elif chat_type == "supergroup":
            if file_info.get("username"):
                source_info = f"\n\nhttps://t.me/{file_info['username']}"
            else:
                source_info = f"\n\nPrivate Group"
        elif chat_type == "private":
            user = file_info.get("from_user", {})
            if user.get("id"):
                source_info = f"\n\nUser ID: {user.get('id')}"
        
        # Combine with original caption
        if original_caption:
            return f"{original_caption}{source_info}"
        else:
            return source_info.strip()
    
    async def forward_file(self, message: Message) -> Optional[Dict]:
        """Forward a single file with rate limiting"""
        try:
            # Get file information
            file_info = self.get_file_info(message)
            
            # Skip if not media or already processed
            if not self.should_process(file_info):
                return None
            
            # Determine target destination based on file size and configuration
            target_destination = self.get_target_destination(file_info)
            if not target_destination:
                # This is now expected behavior when forwarding is disabled for this file type
                logger.debug(f"No target destination available for file type (size: {file_info.get('file_size', 0)} bytes)")
                return None
                
            # Preserve original caption
            caption = file_info.get("caption", "")
            
            # Check if file is over 100MB
            file_size = file_info.get("file_size", 0)
            file_size_mb = file_size / (1024 * 1024)
            is_large_file = file_size > TELEGRAM_MAX_FILE_SIZE
            
            # Configure retry settings
            max_attempts = MAX_RETRY_ATTEMPTS
            
            # 1. Try copy_message first (no forwarded tag)
            try:
                # Using copy_message method to forward without tag
                forwarded_msg = await self.bot.copy_message(
                    chat_id=target_destination,
                    from_chat_id=file_info["chat_id"],
                    message_id=file_info["message_id"],
                    caption=caption,
                    parse_mode=None,  # Avoid markdown parsing errors
                    read_timeout=60
                )

                # Success! Mark as processed and return
                result_info = {
                    "message_id": forwarded_msg.message_id,
                    "chat_id": target_destination,
                    "success": True,
                    "method": "copy_message",
                    "timestamp": int(time.time()),
                    "source": file_info
                }
                self.dedup.add_to_dedup(file_info["file_unique_id"], result_info)
                # Successfully forwarded via copy_message (no forward tag)
                return result_info
                
            except (Forbidden, BadRequest, TimedOut) as e:
                error_msg = str(e).lower()
                logger.warning(f"copy_message failed: {e}")
                
                # 2. Try forward_message as fallback (has forwarded tag)
                try:
                    logger.info(f"Using forward_message method as fallback")
                    forwarded = await self.bot.forward_message(
                        chat_id=target_destination,
                        from_chat_id=file_info["chat_id"],
                        message_id=file_info["message_id"],
                        disable_notification=True,
                        read_timeout=60
                    )

                    # Success with forward tag - mark as processed and return
                    result_info = {
                        "message_id": forwarded.message_id,
                        "chat_id": target_destination,
                        "success": True,
                        "method": "direct_forward",
                        "timestamp": int(time.time()),
                        "source": file_info
                    }
                    self.dedup.add_to_dedup(file_info["file_unique_id"], result_info)
                    logger.info(f"Successfully forwarded {file_info['media_type']} via forward_message (has forward tag)")
                    return result_info
                    
                except (Forbidden, BadRequest) as forward_error:
                    forward_error_msg = str(forward_error).lower()
                    logger.warning(f"forward_message also failed: {forward_error}")
                    
                    # Content is restricted or has other issues
                    # For large files over 100MB, just skip
                    if file_size > TELEGRAM_MAX_FILE_SIZE:
                        logger.info(f"Skipping large file ({file_size_mb:.2f} MB) as forwarding failed")
                        return None
                        
                    # For smaller files, try download and reupload if it's a restriction issue
                    if "restrict" in error_msg or "restrict" in forward_error_msg:
                        logger.info(f"Content appears restricted, trying download/upload for file under 100MB")
                        return await self._fallback_reupload(file_info, target_destination, caption)
                    
                    # Other errors - just skip
                    logger.error(f"All forwarding methods failed for file, skipping")
                    return None
                    
            except Exception as e:
                # Unknown error
                logger.error(f"Error forwarding file: {e}", exc_info=True)
                
                # Skip large files, try download/upload for small ones
                if file_size > TELEGRAM_MAX_FILE_SIZE:
                    logger.info(f"Skipping large file ({file_size_mb:.2f} MB) due to error")
                    return None
                
                # For smaller files, try download/upload as last resort
                logger.info(f"Trying download/upload for file under 100MB")
                return await self._fallback_reupload(file_info, target_destination, caption)
                
        except Exception as e:
            logger.error(f"Unexpected error in forward_file: {e}", exc_info=True)
            return None
        
    async def _fallback_reupload(self, file_info: Dict, target_channel: int, caption: str) -> Optional[Dict]:
        """Fallback method: download and re-upload for files that can't be copied/forwarded directly"""
        try:
            file_unique_id = file_info.get("file_unique_id", "unknown")
            file_size = file_info.get("file_size", 0)
            file_size_mb = file_size / (1024 * 1024)
            
            logger.info(f"Using fallback reupload for file: {file_unique_id} (Size: {file_size_mb:.2f}MB)")
            
            # Safety check for file size - don't even try for files >100MB
            if file_size > TELEGRAM_MAX_FILE_SIZE:
                logger.error(f"File too large, skipping download/upload: {file_size_mb:.2f}MB")
                return None
            
            # Additional safety check for practical upload limit (~50MB)  
            if file_size > TELEGRAM_BOT_MAX_FILE_SIZE:
                logger.warning(f"File size ({file_size_mb:.2f}MB) exceeds practical bot upload limit (~50MB), but trying anyway")
            
            # Get the file from Telegram
            try:
                file = await self.bot.get_file(file_info["file_id"])
                logger.info(f"Successfully retrieved file info for {file_unique_id}")
            except Exception as e:
                logger.error(f"Failed to get file info: {e}")
                return None
            
            # Create temporary file with meaningful name based on media type
            media_type = file_info.get("media_type", "media")
            temp_file = os.path.join(
                tempfile.gettempdir(), 
                f"tg_{media_type}_{file_unique_id}_{uuid.uuid4().hex[:8]}"
            )
            
            try:
                # Download to temporary file
                try:
                    logger.info(f"Downloading {media_type} file ({file_size_mb:.2f}MB) to: {temp_file}")
                    start_time = time.time()
                    
                    await file.download_to_drive(custom_path=temp_file)
                    
                    download_time = time.time() - start_time
                    logger.info(f"Download completed in {download_time:.2f}s ({file_size_mb/download_time:.2f}MB/s)")
                    
                except AttributeError:
                    # Fallback for older python-telegram-bot versions
                    logger.info(f"Fallback to older download method")
                    await file.download(custom_path=temp_file)
                    logger.info(f"Download completed using fallback method")
                except Exception as e:
                    logger.error(f"File download failed: {e}")
                    return None
                    
                # Read file into memory
                logger.info(f"Reading file from temporary location")
                with open(temp_file, "rb") as f:
                    file_content = f.read()
                file_size_mb = len(file_content) / (1024 * 1024)
                logger.info(f"File read into memory, size: {file_size_mb:.2f}MB")
                    
                # Upload based on media type
                upload_start_time = time.time()
                
                if file_info["media_type"] == "video":
                    try:
                        logger.info(f"Uploading video ({file_size_mb:.2f}MB) to destination {target_channel}")
                        sent_msg = await self.bot.send_video(
                            chat_id=target_channel,
                            video=file_content,
                            caption=caption
                        )
                        logger.info(f"Video upload successful")
                    except Exception as e:
                        logger.error(f"Error uploading video: {e}")
                        return None
                    
                elif file_info["media_type"] == "document":
                    try:
                        logger.info(f"Uploading document ({file_size_mb:.2f}MB) to destination {target_channel}")
                        sent_msg = await self.bot.send_document(
                            chat_id=target_channel,
                            document=file_content,
                            caption=caption
                        )
                        logger.info(f"Document upload successful")
                    except Exception as e:
                        logger.error(f"Error uploading document: {e}")
                        return None
                else:
                    logger.error(f"Unknown media type: {file_info['media_type']}")
                    return None
                    
                upload_time = time.time() - upload_start_time
                logger.info(f"Upload completed in {upload_time:.2f}s ({file_size_mb/upload_time:.2f}MB/s)")
                    
                # Mark as processed in deduplication system
                result_info = {
                    "message_id": sent_msg.message_id,
                    "chat_id": target_channel,
                    "success": True,
                    "method": "fallback_reupload",
                    "timestamp": int(time.time()),
                    "source": file_info
                }
                self.dedup.add_to_dedup(file_info["file_unique_id"], result_info)
                
                logger.info(
                    f"Successfully reuploaded {file_info['media_type']} (Size: {file_size_mb:.2f}MB)"
                )
                
                return result_info
                
            finally:
                # Clean up temporary file
                try:
                    if os.path.exists(temp_file):
                        logger.info(f"Removing temporary file: {temp_file}")
                        os.unlink(temp_file)
                except Exception as e:
                    logger.warning(f"Error removing temporary file: {e}")
                    
        except Exception as e:
            logger.error(f"Fallback reupload failed: {e}")
            return None
            
    async def process_queue(self):
        """Process the media queue"""
        # Try to get and process queue items
        while True:
            item = await self.queue.get_next()
            if not item:
                break
                
            try:
                # Try to process the item
                result = await self.forward_file(item["message"])
                
                if result:
                    # Successfully processed
                    await self.queue.mark_completed(item)
                else:
                    # Failed to process
                    await self.queue.mark_failed(item, "Failed to forward file")
            except Exception as e:
                # Error during processing
                logger.error(f"Error processing queue item: {e}")
                await self.queue.mark_failed(item, str(e))
                
    async def upload_media_batch(self, messages: List[Message]) -> Dict:
        """Process multiple files in batch mode"""
        results = {
            "total": len(messages),
            "success": 0,
            "failed": 0,
            "skipped": 0
        }
        
        for message in messages:
            # Extract file info
            file_info = self.get_file_info(message)
            
            # Check if we should process this file
            if not self.should_process(file_info):
                results["skipped"] += 1
                continue
                
            # Try to forward the file
            result = await self.forward_file(message)
            
            if result:
                results["success"] += 1
            else:
                results["failed"] += 1
                
        return results
            
async def handle_new_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle incoming messages with media to automatically forward"""
    # Check if forwarding is enabled at all
    if not FORWARDING_ENABLED:
        return

    # Handle both regular messages and channel posts
    message = update.message or update.channel_post
    if not message:
        return

    if not context.bot:
        logger.error("No bot in context")
        return

    # Quick check for media - return early if no media
    if not (message.video or message.document):
        return

    # Skip messages from backup destinations to avoid loops
    chat_id = update.effective_chat.id
    backup_destinations = []
    # Note: Small files go to user private chats, so we only need to check large files channel
    # User private chats won't trigger this handler anyway since they're not groups/channels
    if LARGE_FILES_FORWARDING_ENABLED:
        backup_destinations.append(BACKUP_LARGE_FILES_CHANNEL)

    if chat_id in backup_destinations:
        return

    # Basic message info logging (only for media messages)
    message_id = message.message_id
    chat_title = getattr(update.effective_chat, 'title', 'Private Chat')
    chat_type = update.effective_chat.type
    
    try:
        processor = MediaProcessor(context.bot)
        file_info = processor.get_file_info(message)
        
        # Process only if it has media and should be processed
        if file_info.get("has_media") and processor.should_process(file_info):
            media_type = file_info.get("media_type", "unknown")
            file_size = file_info.get("file_size", 0)
            file_size_mb = file_size / (1024 * 1024)

            # Check if forwarding is enabled for this file size
            if not is_forwarding_enabled_for_file_size(file_size):
                logger.debug(f"Skipping {media_type} file ({file_size_mb:.2f} MB) - forwarding disabled for this file size")
                return

            # Log file size info
            logger.info(f"Processing {media_type} file ({file_size_mb:.2f} MB) from {chat_title}")

            # Process files with forward_file which now handles all the logic
            # It will try copy_message, then forward_message, then download/upload for small files
            result = await processor.forward_file(message)

            if result:
                logger.info(f"Successfully forwarded {media_type} ({file_size_mb:.2f} MB) to {result.get('chat_id')}")
            else:
                # If forwarding failed entirely, log it but don't queue
                if file_size > TELEGRAM_MAX_FILE_SIZE:
                    logger.warning(f"Skipped large file {media_type} ({file_size_mb:.2f} MB) - forwarding failed")
                else:
                    logger.warning(f"Failed to forward {media_type} ({file_size_mb:.2f} MB) - all methods failed")
                
    except Exception as e:
        logger.error(f"Error handling message {message_id}: {str(e)}")
        import traceback
        logger.error(f"Full exception: {traceback.format_exc()}")

# Modify the queue processing to handle data instead of Message objects
async def process_queue_item(processor: MediaProcessor, item: dict):
    """Process a single queue item from the queue as a fallback operation"""
    try:
        # Check if we have a file_unique_id to check for duplicates
        file_unique_id = None
        
        # Extract file_unique_id from document or video
        if "document" in item and "file_unique_id" in item["document"]:
            file_unique_id = item["document"]["file_unique_id"]
        elif "video" in item and "file_unique_id" in item["video"]:
            file_unique_id = item["video"]["file_unique_id"]
            
        # Check for duplicate if we have a file_unique_id
        if file_unique_id and processor.dedup.is_duplicate(file_unique_id):
            logger.info(f"Skipping queued duplicate: {file_unique_id}")
            await processor.queue.mark_completed(item)
            return
            
        # Log that we're processing a queued item
        logger.info(f"Processing queued item with ID: {file_unique_id}")
        
        # Create a pseudo file_info from item data
        file_info = {
            "message_id": item.get("message_id"),
            "chat_id": item.get("chat_id"),
            "has_media": True,
            "file_unique_id": file_unique_id,
        }
        
        # Add media specific details
        if "document" in item:
            file_info.update({
                "media_type": "document",
                "file_id": item["document"].get("file_id"),
                "file_size": item["document"].get("file_size", 0),
                "mime_type": item["document"].get("mime_type"),
            })
            size_mb = item["document"].get("file_size", 0) / (1024 * 1024)
            logger.info(f"Processing queued document ({size_mb:.2f} MB)")
        elif "video" in item:
            file_info.update({
                "media_type": "video",
                "file_id": item["video"].get("file_id"),
                "file_size": item["video"].get("file_size", 0),
                "mime_type": "video/mp4",
            })
            size_mb = item["video"].get("file_size", 0) / (1024 * 1024)
            logger.info(f"Processing queued video ({size_mb:.2f} MB)")
        
        # Process the queued item with fallback_reupload
        target_channel = processor.get_target_channel(file_info)
        logger.info(f"Using fallback reupload method for queued item to channel {target_channel}")
        
        # Empty caption for queued items to avoid parsing errors
        result = await processor._fallback_reupload(file_info, target_channel, "")
        
        if result:
            await processor.queue.mark_completed(item)
            logger.info(f"Queue item processed successfully: {file_unique_id}")
        else:
            # If file is too large for download/upload, mark as failed but don't retry
            if file_info.get("file_size", 0) > TELEGRAM_BOT_MAX_FILE_SIZE:  # ~50MB safety limit
                logger.warning(f"File too large for download/upload fallback: {file_info.get('file_size', 0)/1024/1024:.2f}MB")
                await processor.queue.mark_failed(item, "File too large for fallback processing")
            else:
                await processor.queue.mark_failed(item, "Failed to process with fallback method")
            
    except Exception as e:
        logger.error(f"Error processing queue item: {e}")
        await processor.queue.mark_failed(item, str(e))

async def process_queue(processor: MediaProcessor):
    """Process multiple queue items concurrently with rate limiting"""
    BATCH_SIZE = 3  # Process 3 items at a time
    RATE_LIMIT = 30  # Number of items to process per minute
    DELAY = 60 / RATE_LIMIT  # Delay between items to maintain rate limit
    
    while True:
        try:
            tasks = []
            start_time = time.time()
            
            # Get up to BATCH_SIZE items
            for _ in range(BATCH_SIZE):
                item = await processor.queue.get_next()
                if not item:
                    break
                    
                # Create task for each item
                task = asyncio.create_task(process_queue_item(processor, item))
                tasks.append(task)
                
                # Add delay between items for rate limiting
                await asyncio.sleep(DELAY)
                
            if not tasks:
                # No items in queue, wait before checking again
                await asyncio.sleep(5)
                continue
                
            # Wait for current batch to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Log any exceptions from the batch
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Error in queue batch item {i}: {result}")
                    
            # Calculate time spent and add delay if needed
            elapsed = time.time() - start_time
            if elapsed < BATCH_SIZE * DELAY:
                await asyncio.sleep(BATCH_SIZE * DELAY - elapsed)
                
        except Exception as e:
            logger.error(f"Error in queue processing: {e}")
            await asyncio.sleep(5)

# Update the start_queue_processing function
async def start_queue_processing(bot):
    """Initialize queue processing when the bot is running"""
    try:
        processor = MediaProcessor(bot)
        await process_queue(processor)
        logger.info("Queue processor started successfully")
    except Exception as e:
        logger.error(f"Error starting queue processor: {e}")

def register_handlers(application):
    """Register message handlers for media forwarding"""
    try:
        # Validate backup channel configuration first
        logger.info("Validating backup channel configuration...")
        small_enabled, large_enabled, overall_enabled = validate_backup_channels()

        if not overall_enabled:
            logger.warning("Media forwarding completely disabled - no backup destinations configured")
            logger.warning("Bot will continue to function normally for other features")
            return

        # Always create a fresh logger file to ensure we capture all logs
        with open("forward.log", "w") as f:
            f.write("# Media forwarding log initialized\n")

        # Create and log the media processor instance
        processor = MediaProcessor(application.bot)
        logger.info("Media processor created")

        # Define better message handler that handles all possible media
        from telegram.ext import MessageHandler, filters

        logger.info("Setting up message handlers for media forwarding")

        # Register handler with "ALL" filter and let our code do the filtering
        # This ensures we get all messages and can log decisions
        application.add_handler(
            MessageHandler(filters.ALL, handle_new_message)
        )

        # Ensure we're handling all update types
        logger.info("Setting allowed_updates to include channel_posts")

        # Log configuration details
        logger.info(f"Media forwarding handlers registered with ALL filter")
        logger.info(f"Configuration summary:")
        logger.info(f"  - Small files forwarding: {'✅ Enabled' if small_enabled else '❌ Disabled'}")
        if small_enabled:
            logger.info(f"    User ID: {BACKUP_SMALL_FILES_USER}")
        logger.info(f"  - Large files forwarding: {'✅ Enabled' if large_enabled else '❌ Disabled'}")
        if large_enabled:
            logger.info(f"    Channel ID: {BACKUP_LARGE_FILES_CHANNEL}")
        logger.info(f"  - File size thresholds:")
        logger.info(f"    Bot upload limit: {TELEGRAM_BOT_MAX_FILE_SIZE/1024/1024} MB")
        logger.info(f"    Large file threshold: {TELEGRAM_MAX_FILE_SIZE/1024/1024} MB")

        logger.info("Media forwarding system initialized and ready")
    except Exception as e:
        # Log detailed exception info
        import traceback
        logger.error(f"Failed to register media handlers: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        logger.error("Bot will continue to function normally for other features")

    logger.info("Media forwarding initialization completed")

def start_background_tasks(application):
    """Start background tasks after the bot is running"""
    # Validate configuration on startup
    logger.info("Validating media forwarding configuration on startup...")
    validate_backup_channels()

    # No background tasks needed anymore
    logger.info("No background tasks to start - using direct forwarding only")

def get_media_forwarding_status():
    """
    Get detailed status of media forwarding configuration.
    Useful for admin commands or debugging.

    Returns:
        dict: Detailed status information
    """
    status = get_forwarding_status()

    # Add more detailed information
    status.update({
        "telegram_bot_max_file_size_mb": TELEGRAM_BOT_MAX_FILE_SIZE / (1024 * 1024),
        "telegram_max_file_size_mb": TELEGRAM_MAX_FILE_SIZE / (1024 * 1024),
        "dedup_file": DEDUP_FILE,
        "max_retry_attempts": MAX_RETRY_ATTEMPTS,
        "retry_delay": RETRY_DELAY
    })

    return status